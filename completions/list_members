# mailman list_members completion                          -*- shell-script -*-

_comp_cmd_list_members()
{
    local cur prev words cword was_split comp_args
    _comp_initialize -s -- "$@" || return

    case $prev in
        -o | --output)
            _comp_compgen_filedir
            return
            ;;
        -d | --digest)
            _comp_compgen -- -W 'mime plain'
            return
            ;;
        -n | --nomail)
            _comp_compgen -- -W 'byadmin byuser bybounce unknown'
            return
            ;;
    esac

    [[ $was_split ]] && return

    if [[ $cur == -* ]]; then
        _comp_compgen -- -W '--output --regular --digest --nomail --fullnames
            --preserve --help'
    else
        # Prefer `list_lists` in the same dir as command
        local pathcmd
        pathcmd=$(type -P -- "$1") && local PATH=${pathcmd%/*}:$PATH
        _comp_compgen -x list_lists mailman_lists
    fi

} &&
    complete -F _comp_cmd_list_members list_members

# ex: filetype=sh
