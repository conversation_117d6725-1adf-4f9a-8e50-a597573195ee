# 3rd party completion loader for commands emitting        -*- shell-script -*-
# their completion using "_${cmdname^^}_COMPLETE=bash_source $cmd".
# This pattern is used by programs built with https://click.palletsprojects.com
#
# This serves as a fallback in case the completion is not installed otherwise.

eval -- "$(
    # shellcheck disable=SC2154
    ucname="${cmdname^^}"
    ucname=${ucname//-/_}
    export "_${ucname}_COMPLETE=bash_source"
    "$1" 2>/dev/null
)"

# ex: filetype=sh
