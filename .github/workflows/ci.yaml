name: ci

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with: # for gitlint
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: 0
      - uses: actions/setup-python@42375524e23c412d93fb67b49958b491fce71c38 # v5.4.0
        with:
          python-version: ">=3.7"
          cache: pip
          cache-dependency-path: |
            .github/requirements.txt
            test/requirements*.txt
      - uses: actions/cache@5a3ec84eff668545956fd18022155c47e93e2684 # v4.2.3
        with:
          path: ~/.cache/pre-commit
          key: pre-commit-${{hashFiles('.pre-commit-config.yaml')}}
      - name: Install dependencies
        run: |
          python3 -m venv venv  # for venv-run
          source venv/bin/activate
          python3 -m pip install -Ur .github/requirements.txt
      - run: venv/bin/gitlint --commits "origin/$GITHUB_BASE_REF..HEAD"
        if: github.event_name == 'pull_request'
      - name: Run pre-commit checks
        run: |
          source venv/bin/activate
          pre-commit run --color=always --all-files --show-diff-on-failure

  distcheck:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - dist: alpine
          - dist: centos7
          - dist: debian10
          - dist: debian10
            bsd: true
            network: none
          - dist: fedoradev
          - dist: ubuntu14
      fail-fast: false
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: googleapis/release-please-action@5792afc6b46e9bb55deda9eda973a18c226bc3fc # v4.1.5
        with:
          config-file: .github/release-please-config.json
          manifest-file: .github/release-please-manifest.json
        id: release
        if: github.event_name == 'push' && matrix.dist == 'alpine'
      # A "container" workflow config would be cleaner here, but comes with
      # some restrictions/oddities: changes root's $HOME to /github/home
      # without changing the actual home dir that can cause some problems,
      # and does not provide a way to run with --network none.
      - name: Run main build
        run: >-
          docker run
          --rm
          --tty
          --env CI=true
          --env BSD=${{matrix.bsd}}
          --env PYTESTFLAGS="--verbose -p no:cacheprovider"
          --env NETWORK=$NETWORK
          --env BASH_COMPLETION_TEST_LIVE_SSH_HOST=localhost
          ${NETWORK:+--network $NETWORK}
          --volume $PWD:/usr/src/bash-completion
          --workdir /usr/src/bash-completion
          ghcr.io/scop/bash-completion/test:${{matrix.dist}}
          test/docker/entrypoint.sh
        env:
          NETWORK: ${{matrix.network}}
      - uses: actions/upload-artifact@6f51ac03b9356f520e9adb1b1b7802705f340c2b # v4.5.0
        with:
          path: |
            bash-completion-*.tar.xz
            sha256sums.txt
        if: matrix.dist == 'alpine'
      - name: Upload release assets
        run: |
          set -x
          gh release upload ${{steps.release.outputs.tag_name}} \
            bash-completion-$(cat version.txt).tar.xz sha256sums.txt
        env:
          GH_TOKEN: ${{github.token}}
        if: steps.release.outputs.release_created
