{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:base", ":enablePreCommit", ":prConcurrentLimit20", ":preserveSemverRanges", ":semanticPrefixChore"], "ignorePresets": [":dependencyDashboard", ":ignoreModulesAndTests", ":semanticPrefixFixDepsChoreOthers"], "semanticCommits": "enabled", "commitMessageTopic": "{{depName}}", "dockerfile": {"enabled": false}, "pip_requirements": {"fileMatch": ["(^|/)requirements[\\w-]*\\.txt$"]}, "regexManagers": [{"fileMatch": ["^\\.pre-commit-config\\.yaml$"], "matchStrings": ["(?<depName>[\\w-]+)(?<currentValue>==[a-z0-9.]+)"], "datasourceTemplate": "pypi"}], "labels": ["bot"], "packageRules": [{"matchFiles": ["test/requirements.txt"], "semanticCommitType": "test"}, {"matchManagers": ["pre-commit"], "commitMessageTopic": "{{depName}}", "semanticCommitScope": "pre-commit"}, {"matchPackagePatterns": ["(^|/)gitlint$"], "versioning": "pep440", "groupName": "gitlint"}, {"matchPackageNames": ["perltidy/perltidy"], "versioning": "regex:^(?<major>\\d{8})$"}, {"matchPackagePatterns": ["(^|/)ruff(-pre-commit)?$"], "groupName": "ruff", "versioning": "pep440"}, {"matchPackageNames": ["scop/pre-commit-shfmt", "shellcheck-py/shellcheck-py"], "versioning": "loose"}]}